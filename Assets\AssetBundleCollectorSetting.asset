%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 185f6993d5150494d98da50e26cb1c25, type: 3}
  m_Name: AssetBundleCollectorSetting
  m_EditorClassIdentifier: 
  ShowPackageView: 1
  EnableAddressable: 0
  LocationToLower: 0
  IncludeAssetGUID: 0
  UniqueBundleName: 0
  ShowEditorAlias: 0
  Packages:
  - PackageName: DefaultPackage
    PackageDesc: 
    Groups:
    - GroupName: Default Group
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/_MyGame/Bundles/DataSO
        CollectorGUID: 075ea2371857bb043bca2221e4e3b4ea
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Effect
        CollectorGUID: 0554691373e2770468d879fdd6063115
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Fonts
        CollectorGUID: e495910d2af44c8438550cfb9b62a255
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Prefabs
        CollectorGUID: bd0433640277d0343a3c01cbd6cbdaa8
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackCollector
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Scenes
        CollectorGUID: 665b08c819155614782213ad0cd3a885
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Shader
        CollectorGUID: cfc4332e7bf6df649937f92e2f59555a
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Sound
        CollectorGUID: 47ed4389a65622346b1a6bbe424b962b
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Textures
        CollectorGUID: 64ab11e249a4cc84683a38a825ba7819
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/Theme
        CollectorGUID: 11285ca027b761f4381a60fcc88a13d7
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackTopDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/_MyGame/Bundles/UI
        CollectorGUID: 181bde3ef771f4641add16e8ed7db1a0
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
